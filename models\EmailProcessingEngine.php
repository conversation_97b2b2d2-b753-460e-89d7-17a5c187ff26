<?php
/**
 * Email Processing Engine
 * 
 * Handles email parsing, ticket number extraction, threading, spam filtering,
 * and integration with the unified messaging system
 */
class EmailProcessingEngine {
    private $db;
    private $settingsModel;
    private $unifiedMessageModel;
    private $spamFilteringEnabled;
    private $maxEmailSizeMB;
    private $attachmentEnabled;
    private $maxAttachmentSizeMB;
    
    public function __construct() {
        $this->db = new Database();
        require_once APPROOT . '/models/SettingsModel.php';
        require_once APPROOT . '/models/UnifiedMessageModel.php';
        $this->settingsModel = new SettingsModel();
        $this->unifiedMessageModel = new UnifiedMessageModel();
        $this->loadSettings();
    }
    
    /**
     * Load processing settings
     */
    private function loadSettings() {
        $this->spamFilteringEnabled = $this->settingsModel->getSetting('email_spam_filtering', '1') === '1';
        $this->maxEmailSizeMB = (int)$this->settingsModel->getSetting('email_max_size_mb', '10');
        $this->attachmentEnabled = $this->settingsModel->getSetting('email_attachment_enabled', '1') === '1';
        $this->maxAttachmentSizeMB = (int)$this->settingsModel->getSetting('email_attachment_max_size_mb', '5');
    }
    
    /**
     * Process a batch of emails
     */
    public function processEmails($emails) {
        $results = [
            'processed' => 0,
            'failed' => 0,
            'ignored' => 0,
            'errors' => []
        ];
        
        foreach ($emails as $email) {
            try {
                $result = $this->processEmail($email);
                
                if ($result['status'] === 'processed') {
                    $results['processed']++;
                } elseif ($result['status'] === 'ignored') {
                    $results['ignored']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = $result['message'];
                }
                
            } catch (Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Email processing error: " . $e->getMessage();
                error_log("EmailProcessingEngine::processEmails - Error: " . $e->getMessage());
            }
        }
        
        return $results;
    }
    
    /**
     * Process individual email
     */
    public function processEmail($email) {
        try {
            // Log email processing attempt
            $logId = $this->logEmailProcessing($email, 'pending');
            
            // Check if already processed
            if ($this->isEmailAlreadyProcessed($email['message_id'])) {
                $this->updateProcessingLog($logId, 'ignored', 'Email already processed');
                return ['status' => 'ignored', 'message' => 'Email already processed'];
            }
            
            // Validate email size
            if ($this->maxEmailSizeMB > 0 && $email['size'] > ($this->maxEmailSizeMB * 1024 * 1024)) {
                $this->updateProcessingLog($logId, 'ignored', 'Email too large');
                return ['status' => 'ignored', 'message' => 'Email too large'];
            }
            
            // Check for auto-reply
            if ($this->isAutoReply($email)) {
                $this->updateProcessingLog($logId, 'ignored', 'Auto-reply detected');
                return ['status' => 'ignored', 'message' => 'Auto-reply ignored'];
            }
            
            // Spam filtering
            if ($this->spamFilteringEnabled) {
                $spamScore = $this->calculateSpamScore($email);
                if ($spamScore > 0.7) {
                    $this->updateProcessingLog($logId, 'ignored', 'Spam detected (score: ' . $spamScore . ')');
                    return ['status' => 'ignored', 'message' => 'Spam detected'];
                }
            }
            
            // Extract or generate ticket number
            $ticketNumber = $this->extractOrGenerateTicketNumber($email);
            
            // Find parent message for threading
            $parentMessageId = $this->findParentMessage($email, $ticketNumber);
            
            // Determine sender user
            $senderUserId = $this->findSenderUser($email['from_email']);
            
            // Get admin users for notification
            $adminUsers = $this->getAdminUsers();
            
            // Process attachments
            $attachments = [];
            if ($this->attachmentEnabled && !empty($email['attachments'])) {
                $attachments = $this->processAttachments($email, $logId);
            }
            
            // Create message for each admin
            $messageIds = [];
            foreach ($adminUsers as $admin) {
                $messageId = $this->createMessage($email, $ticketNumber, $parentMessageId, $senderUserId, $admin->id);
                if ($messageId) {
                    $messageIds[] = $messageId;
                    
                    // Link attachments to message
                    foreach ($attachments as $attachment) {
                        $this->linkAttachmentToMessage($messageId, $attachment);
                    }
                }
            }
            
            if (!empty($messageIds)) {
                $this->updateProcessingLog($logId, 'processed', 'Created ' . count($messageIds) . ' messages', $messageIds[0], $ticketNumber);
                
                // Send auto-reply if enabled
                if ($this->settingsModel->getSetting('email_auto_reply_enabled', '1') === '1') {
                    $this->sendAutoReply($email, $ticketNumber);
                }
                
                return ['status' => 'processed', 'message' => 'Email processed successfully', 'ticket' => $ticketNumber];
            } else {
                $this->updateProcessingLog($logId, 'failed', 'Failed to create messages');
                return ['status' => 'failed', 'message' => 'Failed to create messages'];
            }
            
        } catch (Exception $e) {
            if (isset($logId)) {
                $this->updateProcessingLog($logId, 'failed', $e->getMessage());
            }
            throw $e;
        }
    }
    
    /**
     * Log email processing attempt
     */
    private function logEmailProcessing($email, $status) {
        $sql = "INSERT INTO email_processing_log 
                (email_message_id, sender_email, subject, processing_status, email_size, attachment_count, is_auto_reply)
                VALUES (:message_id, :sender, :subject, :status, :size, :attachments, :auto_reply)";
        
        $this->db->query($sql);
        $this->db->bind(':message_id', $email['message_id']);
        $this->db->bind(':sender', $email['from_email']);
        $this->db->bind(':subject', $email['subject']);
        $this->db->bind(':status', $status);
        $this->db->bind(':size', $email['size']);
        $this->db->bind(':attachments', count($email['attachments']));
        $this->db->bind(':auto_reply', $this->isAutoReply($email) ? 1 : 0);
        
        $this->db->execute();
        return $this->db->lastInsertId();
    }
    
    /**
     * Update processing log
     */
    private function updateProcessingLog($logId, $status, $message = null, $messageId = null, $ticketNumber = null) {
        $sql = "UPDATE email_processing_log 
                SET processing_status = :status, error_message = :message, message_id = :msg_id, 
                    ticket_number = :ticket, processed_at = NOW()
                WHERE id = :id";
        
        $this->db->query($sql);
        $this->db->bind(':status', $status);
        $this->db->bind(':message', $message);
        $this->db->bind(':msg_id', $messageId);
        $this->db->bind(':ticket', $ticketNumber);
        $this->db->bind(':id', $logId);
        
        return $this->db->execute();
    }
    
    /**
     * Check if email already processed
     */
    private function isEmailAlreadyProcessed($messageId) {
        $sql = "SELECT id FROM email_processing_log WHERE email_message_id = :message_id AND processing_status = 'processed'";
        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        return $this->db->single() !== false;
    }
    
    /**
     * Detect auto-reply emails
     */
    private function isAutoReply($email) {
        $subject = strtolower($email['subject']);
        $body = strtolower($email['body']);
        
        // Common auto-reply indicators
        $autoReplyIndicators = [
            'auto-reply', 'automatic reply', 'out of office', 'vacation', 'away message',
            'delivery status notification', 'undelivered mail', 'mail delivery failed',
            'postmaster', 'mailer-daemon', 'no-reply', 'noreply', 'do not reply'
        ];
        
        foreach ($autoReplyIndicators as $indicator) {
            if (strpos($subject, $indicator) !== false || strpos($body, $indicator) !== false) {
                return true;
            }
        }
        
        // Check for auto-reply headers (would need to be passed from email retrieval)
        if (isset($email['auto_submitted']) || isset($email['x_auto_response_suppress'])) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Calculate spam score
     */
    private function calculateSpamScore($email) {
        $score = 0.0;
        $subject = strtolower($email['subject']);
        $body = strtolower($email['body']);
        
        // Excessive caps
        if (preg_match_all('/[A-Z]/', $email['subject']) > (strlen($email['subject']) * 0.7)) {
            $score += 0.3;
        }
        
        // Spam keywords
        $spamKeywords = ['urgent!!!', 'act now', 'limited time', 'free money', 'click here', 'viagra', 'casino'];
        foreach ($spamKeywords as $keyword) {
            if (strpos($subject, $keyword) !== false || strpos($body, $keyword) !== false) {
                $score += 0.2;
            }
        }
        
        // Excessive links
        $linkCount = preg_match_all('/https?:\/\//', $email['body']);
        if ($linkCount > 5) {
            $score += 0.3;
        }
        
        // No actual content
        if (strlen(trim(strip_tags($email['body']))) < 10) {
            $score += 0.2;
        }
        
        return min(1.0, $score);
    }
    
    /**
     * Extract ticket number from email or generate new one
     */
    private function extractOrGenerateTicketNumber($email) {
        // Try to extract existing ticket number from subject
        $subject = $email['subject'];
        $pattern = '/\[([A-Z]+-\d{4}-\d{3,})\]/';
        
        if (preg_match($pattern, $subject, $matches)) {
            return $matches[1];
        }
        
        // Check references and in-reply-to for ticket numbers
        $references = $email['references'] . ' ' . $email['in_reply_to'];
        if (preg_match($pattern, $references, $matches)) {
            return $matches[1];
        }
        
        // Generate new ticket number
        return $this->generateTicketNumber();
    }
    
    /**
     * Generate new ticket number
     */
    private function generateTicketNumber() {
        $prefix = $this->settingsModel->getSetting('ticket_number_prefix', 'RER');
        $year = date('Y');
        
        // Get next sequence number
        $sql = "SELECT COALESCE(MAX(sequence), 0) + 1 as next_seq FROM ticket_numbers WHERE prefix = :prefix AND year = :year";
        $this->db->query($sql);
        $this->db->bind(':prefix', $prefix);
        $this->db->bind(':year', $year);
        $result = $this->db->single();
        $sequence = $result->next_seq ?? 1;
        
        $ticketNumber = sprintf('%s-%s-%03d', $prefix, $year, $sequence);
        
        // Insert ticket number record
        $sql = "INSERT INTO ticket_numbers (ticket_number, prefix, year, sequence) VALUES (:ticket, :prefix, :year, :sequence)";
        $this->db->query($sql);
        $this->db->bind(':ticket', $ticketNumber);
        $this->db->bind(':prefix', $prefix);
        $this->db->bind(':year', $year);
        $this->db->bind(':sequence', $sequence);
        $this->db->execute();
        
        return $ticketNumber;
    }
    
    /**
     * Find parent message for threading
     */
    private function findParentMessage($email, $ticketNumber) {
        // First try to find by ticket number
        $sql = "SELECT id FROM messages WHERE ticket_number = :ticket ORDER BY created_at DESC LIMIT 1";
        $this->db->query($sql);
        $this->db->bind(':ticket', $ticketNumber);
        $result = $this->db->single();
        
        if ($result) {
            return $result->id;
        }
        
        // Try by email message ID threading
        if (!empty($email['in_reply_to'])) {
            $sql = "SELECT id FROM messages WHERE email_message_id = :email_id ORDER BY created_at DESC LIMIT 1";
            $this->db->query($sql);
            $this->db->bind(':email_id', $email['in_reply_to']);
            $result = $this->db->single();
            
            if ($result) {
                return $result->id;
            }
        }
        
        return null;
    }
    
    /**
     * Find sender user by email
     */
    private function findSenderUser($email) {
        $sql = "SELECT id FROM users WHERE email = :email AND status = 'active' LIMIT 1";
        $this->db->query($sql);
        $this->db->bind(':email', $email);
        $result = $this->db->single();
        
        return $result ? $result->id : 1; // Default to system user (ID 1)
    }
    
    /**
     * Get all admin users
     */
    private function getAdminUsers() {
        $sql = "SELECT id, name, email FROM users WHERE role = 'admin' AND status = 'active'";
        $this->db->query($sql);
        return $this->db->resultSet();
    }

    /**
     * Create message in unified messaging system
     */
    private function createMessage($email, $ticketNumber, $parentMessageId, $senderUserId, $adminUserId) {
        // Prepare subject with ticket number
        $subject = $email['subject'];
        if (strpos($subject, '[' . $ticketNumber . ']') === false) {
            $subject = $subject . ' [' . $ticketNumber . ']';
        }

        // Prepare message body
        $message = $email['body'];

        // Add original sender info if not a registered user
        if ($senderUserId === 1 && !empty($email['from_email'])) {
            $senderInfo = "Original sender: " . $email['from_name'] . " <" . $email['from_email'] . ">\n";
            $senderInfo .= "Received: " . $email['date'] . "\n\n";
            $message = $senderInfo . $message;
        }

        // Create message using unified messaging system
        $messageId = $this->unifiedMessageModel->sendMessage(
            $senderUserId,
            $adminUserId,
            $subject,
            $message,
            null, // show_id
            'email', // message_type
            false, // requires_reply
            $parentMessageId
        );

        if ($messageId) {
            // Update message with email-specific fields
            $sql = "UPDATE messages SET
                    ticket_number = :ticket,
                    email_message_id = :email_id,
                    original_sender_email = :sender_email
                    WHERE id = :id";

            $this->db->query($sql);
            $this->db->bind(':ticket', $ticketNumber);
            $this->db->bind(':email_id', $email['message_id']);
            $this->db->bind(':sender_email', $senderUserId === 1 ? $email['from_email'] : null);
            $this->db->bind(':id', $messageId);
            $this->db->execute();
        }

        return $messageId;
    }

    /**
     * Process email attachments
     */
    private function processAttachments($email, $logId) {
        $processedAttachments = [];

        if (empty($email['attachments'])) {
            return $processedAttachments;
        }

        // Create attachments directory if it doesn't exist
        $attachmentDir = APPROOT . '/uploads/email_attachments/' . date('Y/m');
        if (!is_dir($attachmentDir)) {
            mkdir($attachmentDir, 0755, true);
        }

        foreach ($email['attachments'] as $attachment) {
            try {
                // Validate attachment
                if (!$this->isAttachmentAllowed($attachment)) {
                    continue;
                }

                // Generate unique filename
                $extension = pathinfo($attachment['filename'], PATHINFO_EXTENSION);
                $storedFilename = uniqid('email_') . '_' . time() . '.' . $extension;
                $filePath = $attachmentDir . '/' . $storedFilename;

                // This would need to be implemented in EmailRetrievalService
                // For now, we'll just log the attachment info
                $processedAttachments[] = [
                    'original_filename' => $attachment['filename'],
                    'stored_filename' => $storedFilename,
                    'file_path' => $filePath,
                    'file_size' => $attachment['size'],
                    'mime_type' => $this->getMimeType($attachment),
                    'part_number' => $attachment['part_number'] ?? 0
                ];

            } catch (Exception $e) {
                error_log("EmailProcessingEngine::processAttachments - Error processing attachment: " . $e->getMessage());
                continue;
            }
        }

        return $processedAttachments;
    }

    /**
     * Check if attachment is allowed
     */
    private function isAttachmentAllowed($attachment) {
        // Check size
        if ($this->maxAttachmentSizeMB > 0 && $attachment['size'] > ($this->maxAttachmentSizeMB * 1024 * 1024)) {
            return false;
        }

        // Check file type
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt', 'zip'];
        $extension = strtolower(pathinfo($attachment['filename'], PATHINFO_EXTENSION));

        return in_array($extension, $allowedExtensions);
    }

    /**
     * Get MIME type from attachment info
     */
    private function getMimeType($attachment) {
        $types = [
            0 => 'text',
            1 => 'multipart',
            2 => 'message',
            3 => 'application',
            4 => 'audio',
            5 => 'image',
            6 => 'video',
            7 => 'other'
        ];

        $type = $types[$attachment['type']] ?? 'application';
        $subtype = $attachment['subtype'] ?? 'octet-stream';

        return $type . '/' . strtolower($subtype);
    }

    /**
     * Link attachment to message
     */
    private function linkAttachmentToMessage($messageId, $attachment) {
        $sql = "INSERT INTO message_attachments
                (message_id, filename, stored_filename, file_path, file_size, mime_type)
                VALUES (:message_id, :filename, :stored_filename, :file_path, :file_size, :mime_type)";

        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':filename', $attachment['original_filename']);
        $this->db->bind(':stored_filename', $attachment['stored_filename']);
        $this->db->bind(':file_path', $attachment['file_path']);
        $this->db->bind(':file_size', $attachment['file_size']);
        $this->db->bind(':mime_type', $attachment['mime_type']);

        return $this->db->execute();
    }

    /**
     * Send auto-reply confirmation
     */
    private function sendAutoReply($email, $ticketNumber) {
        try {
            // Get auto-reply template
            $sql = "SELECT subject, body FROM email_templates WHERE name = 'Auto Reply Confirmation' AND is_active = 1 LIMIT 1";
            $this->db->query($sql);
            $template = $this->db->single();

            if (!$template) {
                return false;
            }

            // Replace template variables
            $subject = str_replace('{{subject}}', $email['subject'], $template->subject);
            $subject = str_replace('{{ticket_number}}', $ticketNumber, $subject);

            $body = str_replace('{{subject}}', $email['subject'], $template->body);
            $body = str_replace('{{ticket_number}}', $ticketNumber, $body);
            $body = str_replace('{{date}}', date('Y-m-d H:i:s'), $body);
            $body = str_replace('{{site_name}}', $this->settingsModel->getSetting('app_name', 'Events and Shows'), $body);

            // Send email using EmailService
            require_once APPROOT . '/models/EmailService.php';
            $emailService = new EmailService();

            if ($emailService->isConfigured()) {
                return $emailService->send($email['from_email'], $subject, nl2br($body), $body);
            }

            return false;

        } catch (Exception $e) {
            error_log("EmailProcessingEngine::sendAutoReply - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean up old processing logs
     */
    public function cleanupOldLogs($daysOld = 30) {
        $sql = "DELETE FROM email_processing_log WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";
        $this->db->query($sql);
        $this->db->bind(':days', $daysOld);
        return $this->db->execute();
    }
}
