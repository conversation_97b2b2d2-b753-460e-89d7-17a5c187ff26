<?php
/**
 * Ticket Number Service
 * 
 * Handles generation and management of unique ticket numbers for email threading
 * Format: RER-2025-001, RER-2025-002, etc.
 */
class TicketNumberService {
    private $db;
    private $settingsModel;
    private $prefix;
    private $currentYear;
    
    public function __construct() {
        $this->db = new Database();
        require_once APPROOT . '/models/SettingsModel.php';
        $this->settingsModel = new SettingsModel();
        $this->loadSettings();
    }
    
    /**
     * Load ticket number settings
     */
    private function loadSettings() {
        $this->prefix = $this->settingsModel->getSetting('ticket_number_prefix', 'RER');
        $this->currentYear = date('Y');
    }
    
    /**
     * Generate a new unique ticket number
     */
    public function generateTicketNumber($messageId = null) {
        try {
            $this->db->beginTransaction();
            
            // Get next sequence number for current year
            $sequence = $this->getNextSequence();
            
            // Format ticket number
            $ticketNumber = sprintf('%s-%s-%03d', $this->prefix, $this->currentYear, $sequence);
            
            // Insert ticket record
            $sql = "INSERT INTO ticket_numbers (ticket_number, prefix, year, sequence, message_id) 
                    VALUES (:ticket, :prefix, :year, :sequence, :message_id)";
            
            $this->db->query($sql);
            $this->db->bind(':ticket', $ticketNumber);
            $this->db->bind(':prefix', $this->prefix);
            $this->db->bind(':year', $this->currentYear);
            $this->db->bind(':sequence', $sequence);
            $this->db->bind(':message_id', $messageId);
            
            if (!$this->db->execute()) {
                throw new Exception('Failed to insert ticket number record');
            }
            
            $this->db->commit();
            return $ticketNumber;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("TicketNumberService::generateTicketNumber - Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get next sequence number for current year
     */
    private function getNextSequence() {
        // Lock table to prevent race conditions
        $this->db->query("LOCK TABLES ticket_numbers WRITE");
        
        try {
            // Get highest sequence for current year and prefix
            $sql = "SELECT COALESCE(MAX(sequence), 0) as max_sequence 
                    FROM ticket_numbers 
                    WHERE prefix = :prefix AND year = :year";
            
            $this->db->query($sql);
            $this->db->bind(':prefix', $this->prefix);
            $this->db->bind(':year', $this->currentYear);
            
            $result = $this->db->single();
            $nextSequence = ($result->max_sequence ?? 0) + 1;
            
            $this->db->query("UNLOCK TABLES");
            return $nextSequence;
            
        } catch (Exception $e) {
            $this->db->query("UNLOCK TABLES");
            throw $e;
        }
    }
    
    /**
     * Validate ticket number format
     */
    public function isValidTicketNumber($ticketNumber) {
        $pattern = '/^[A-Z]+-\d{4}-\d{3,}$/';
        return preg_match($pattern, $ticketNumber) === 1;
    }
    
    /**
     * Extract ticket number from text
     */
    public function extractTicketNumber($text) {
        $pattern = '/\[?([A-Z]+-\d{4}-\d{3,})\]?/';
        
        if (preg_match($pattern, $text, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
    
    /**
     * Check if ticket number exists
     */
    public function ticketExists($ticketNumber) {
        $sql = "SELECT id FROM ticket_numbers WHERE ticket_number = :ticket";
        $this->db->query($sql);
        $this->db->bind(':ticket', $ticketNumber);
        return $this->db->single() !== false;
    }
    
    /**
     * Get ticket information
     */
    public function getTicketInfo($ticketNumber) {
        $sql = "SELECT t.*, m.subject, m.created_at as message_created 
                FROM ticket_numbers t
                LEFT JOIN messages m ON t.message_id = m.id
                WHERE t.ticket_number = :ticket";
        
        $this->db->query($sql);
        $this->db->bind(':ticket', $ticketNumber);
        return $this->db->single();
    }
    
    /**
     * Link ticket to message
     */
    public function linkTicketToMessage($ticketNumber, $messageId) {
        $sql = "UPDATE ticket_numbers SET message_id = :message_id WHERE ticket_number = :ticket";
        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':ticket', $ticketNumber);
        return $this->db->execute();
    }
    
    /**
     * Get all messages for a ticket (threading)
     */
    public function getTicketMessages($ticketNumber) {
        $sql = "SELECT m.*, u.name as sender_name 
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                WHERE m.ticket_number = :ticket
                ORDER BY m.created_at ASC";
        
        $this->db->query($sql);
        $this->db->bind(':ticket', $ticketNumber);
        return $this->db->resultSet();
    }
    
    /**
     * Get ticket statistics
     */
    public function getTicketStats($year = null) {
        if (!$year) {
            $year = $this->currentYear;
        }
        
        $sql = "SELECT 
                    COUNT(*) as total_tickets,
                    COUNT(DISTINCT DATE(created_at)) as active_days,
                    MIN(sequence) as first_sequence,
                    MAX(sequence) as last_sequence
                FROM ticket_numbers 
                WHERE year = :year AND prefix = :prefix";
        
        $this->db->query($sql);
        $this->db->bind(':year', $year);
        $this->db->bind(':prefix', $this->prefix);
        
        return $this->db->single();
    }
    
    /**
     * Clean up orphaned tickets (no associated message)
     */
    public function cleanupOrphanedTickets($daysOld = 7) {
        $sql = "DELETE FROM ticket_numbers 
                WHERE message_id IS NULL 
                AND created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";
        
        $this->db->query($sql);
        $this->db->bind(':days', $daysOld);
        return $this->db->execute();
    }
    
    /**
     * Update ticket number settings
     */
    public function updateSettings($prefix = null, $resetCounter = false) {
        if ($prefix && $prefix !== $this->prefix) {
            $this->settingsModel->setSetting('ticket_number_prefix', $prefix);
            $this->prefix = $prefix;
        }
        
        if ($resetCounter) {
            // This would reset the counter for the current year
            // Be careful with this in production
            $sql = "DELETE FROM ticket_numbers WHERE year = :year AND prefix = :prefix";
            $this->db->query($sql);
            $this->db->bind(':year', $this->currentYear);
            $this->db->bind(':prefix', $this->prefix);
            $this->db->execute();
        }
        
        return true;
    }
    
    /**
     * Generate ticket number for contact form
     */
    public function generateContactFormTicket($messageId) {
        return $this->generateTicketNumber($messageId);
    }
    
    /**
     * Add ticket number to subject line
     */
    public function addTicketToSubject($subject, $ticketNumber) {
        // Check if ticket already in subject
        if (strpos($subject, '[' . $ticketNumber . ']') !== false) {
            return $subject;
        }
        
        // Add ticket number to end of subject
        return $subject . ' [' . $ticketNumber . ']';
    }
    
    /**
     * Remove ticket number from subject line
     */
    public function removeTicketFromSubject($subject) {
        $pattern = '/\s*\[[A-Z]+-\d{4}-\d{3,}\]\s*/';
        return trim(preg_replace($pattern, '', $subject));
    }
    
    /**
     * Get recent tickets
     */
    public function getRecentTickets($limit = 10) {
        $sql = "SELECT t.*, m.subject, m.created_at as message_created, u.name as sender_name
                FROM ticket_numbers t
                LEFT JOIN messages m ON t.message_id = m.id
                LEFT JOIN users u ON m.from_user_id = u.id
                ORDER BY t.created_at DESC
                LIMIT :limit";
        
        $this->db->query($sql);
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }
    
    /**
     * Search tickets
     */
    public function searchTickets($query, $limit = 20) {
        $sql = "SELECT t.*, m.subject, m.message, m.created_at as message_created, u.name as sender_name
                FROM ticket_numbers t
                LEFT JOIN messages m ON t.message_id = m.id
                LEFT JOIN users u ON m.from_user_id = u.id
                WHERE t.ticket_number LIKE :query 
                   OR m.subject LIKE :query 
                   OR m.message LIKE :query
                ORDER BY t.created_at DESC
                LIMIT :limit";
        
        $this->db->query($sql);
        $searchQuery = '%' . $query . '%';
        $this->db->bind(':query', $searchQuery);
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }
}
