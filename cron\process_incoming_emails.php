<?php
/**
 * Process Incoming Emails Cron Job
 * 
 * Fetches emails from POP3/IMAP server, processes them through the email processing engine,
 * creates messages in the unified messaging system, and sends notifications to admins.
 * 
 * Runs every 5 minutes via cron job
 */

// Define APPROOT (go up one level from /cron/ to root)
define('APPROOT', dirname(__FILE__, 2));

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting for debugging
ini_set('display_errors', 0); // Don't display errors in cron
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/models/SettingsModel.php';
require_once APPROOT . '/models/EmailRetrievalService.php';
require_once APPROOT . '/models/EmailProcessingEngine.php';

// Create instances
$db = new Database();
$settingsModel = new SettingsModel();

// Check if email processing is enabled
$processingEnabled = $settingsModel->getSetting('email_processing_enabled', '1') === '1';
if (!$processingEnabled) {
    error_log("process_incoming_emails.php - Email processing is disabled");
    exit(0);
}

// Initialize services
$emailRetrieval = new EmailRetrievalService();
$emailProcessor = new EmailProcessingEngine();

// Log start of processing
$startTime = microtime(true);
error_log("process_incoming_emails.php - Starting email processing at " . date('Y-m-d H:i:s'));

try {
    // Check if email retrieval is configured
    if (!$emailRetrieval->isConfigured()) {
        error_log("process_incoming_emails.php - Email server not configured");
        exit(1);
    }
    
    // Test connection
    $connectionTest = $emailRetrieval->testConnection();
    if (!$connectionTest['success']) {
        error_log("process_incoming_emails.php - Connection test failed: " . $connectionTest['message']);
        exit(1);
    }
    
    // Fetch emails from server
    $fetchLimit = (int)$settingsModel->getSetting('email_fetch_limit', '50');
    $fetchResult = $emailRetrieval->fetchEmails($fetchLimit);
    
    if (!$fetchResult['success']) {
        error_log("process_incoming_emails.php - Failed to fetch emails: " . $fetchResult['message']);
        exit(1);
    }
    
    $emails = $fetchResult['emails'];
    $emailCount = count($emails);
    
    if ($emailCount === 0) {
        error_log("process_incoming_emails.php - No new emails to process");
        exit(0);
    }
    
    error_log("process_incoming_emails.php - Fetched {$emailCount} emails for processing");
    
    // Process emails
    $processingResults = $emailProcessor->processEmails($emails);
    
    // Handle email deletion if enabled
    $deleteAfterProcessing = $settingsModel->getSetting('email_delete_after_processing', '1') === '1';
    $deletedCount = 0;
    
    if ($deleteAfterProcessing && $processingResults['processed'] > 0) {
        try {
            // Mark processed emails for deletion
            foreach ($emails as $email) {
                if (isset($email['message_number'])) {
                    $emailRetrieval->markForDeletion($email['message_number']);
                    $deletedCount++;
                }
            }
            
            // Expunge deleted emails
            if ($deletedCount > 0) {
                $emailRetrieval->expungeDeleted();
                error_log("process_incoming_emails.php - Marked {$deletedCount} emails for deletion");
            }
            
        } catch (Exception $e) {
            error_log("process_incoming_emails.php - Error deleting emails: " . $e->getMessage());
        }
    }
    
    // Log processing results
    $endTime = microtime(true);
    $processingTime = round($endTime - $startTime, 2);
    
    $logMessage = "process_incoming_emails.php - Processing completed in {$processingTime}s: ";
    $logMessage .= "Processed: {$processingResults['processed']}, ";
    $logMessage .= "Failed: {$processingResults['failed']}, ";
    $logMessage .= "Ignored: {$processingResults['ignored']}";
    
    if ($deleteAfterProcessing) {
        $logMessage .= ", Deleted: {$deletedCount}";
    }
    
    error_log($logMessage);
    
    // Log any errors
    if (!empty($processingResults['errors'])) {
        foreach ($processingResults['errors'] as $error) {
            error_log("process_incoming_emails.php - Processing error: " . $error);
        }
    }
    
    // Update heartbeat file
    updateHeartbeat($processingResults, $processingTime);
    
    // Cleanup old logs if needed
    cleanupOldData();
    
    exit(0);
    
} catch (Exception $e) {
    $endTime = microtime(true);
    $processingTime = round($endTime - $startTime, 2);
    
    error_log("process_incoming_emails.php - Fatal error after {$processingTime}s: " . $e->getMessage());
    error_log("process_incoming_emails.php - Stack trace: " . $e->getTraceAsString());
    
    // Update heartbeat with error
    updateHeartbeat(['error' => $e->getMessage()], $processingTime);
    
    exit(1);
}

/**
 * Update heartbeat file to track cron job health
 */
function updateHeartbeat($results, $processingTime) {
    try {
        $heartbeatFile = APPROOT . '/logs/email_processing_heartbeat.txt';
        $heartbeatDir = dirname($heartbeatFile);
        
        // Create logs directory if it doesn't exist
        if (!is_dir($heartbeatDir)) {
            mkdir($heartbeatDir, 0755, true);
        }
        
        $heartbeatData = [
            'last_run' => gmdate('Y-m-d H:i:s'),
            'processing_time' => $processingTime,
            'status' => isset($results['error']) ? 'error' : 'success',
            'processed' => $results['processed'] ?? 0,
            'failed' => $results['failed'] ?? 0,
            'ignored' => $results['ignored'] ?? 0,
            'error' => $results['error'] ?? null
        ];
        
        file_put_contents($heartbeatFile, json_encode($heartbeatData, JSON_PRETTY_PRINT));
        
    } catch (Exception $e) {
        error_log("process_incoming_emails.php - Failed to update heartbeat: " . $e->getMessage());
    }
}

/**
 * Cleanup old data
 */
function cleanupOldData() {
    global $emailProcessor, $settingsModel;
    
    try {
        // Cleanup old processing logs (older than 30 days)
        $logRetentionDays = (int)$settingsModel->getSetting('email_log_retention_days', '30');
        $emailProcessor->cleanupOldLogs($logRetentionDays);
        
        // Cleanup orphaned tickets (older than 7 days)
        require_once APPROOT . '/models/TicketNumberService.php';
        $ticketService = new TicketNumberService();
        $ticketService->cleanupOrphanedTickets(7);
        
    } catch (Exception $e) {
        error_log("process_incoming_emails.php - Cleanup error: " . $e->getMessage());
    }
}

/**
 * Send alert to admins if processing fails repeatedly
 */
function sendFailureAlert($errorMessage) {
    try {
        // Check if we've already sent an alert recently
        $alertFile = APPROOT . '/logs/email_processing_alert.txt';
        $alertCooldown = 3600; // 1 hour
        
        if (file_exists($alertFile)) {
            $lastAlert = filemtime($alertFile);
            if ((time() - $lastAlert) < $alertCooldown) {
                return; // Don't spam alerts
            }
        }
        
        // Load unified messaging to send alert
        require_once APPROOT . '/models/UnifiedMessageModel.php';
        $messageModel = new UnifiedMessageModel();
        
        // Get admin users
        $db = new Database();
        $db->query("SELECT id FROM users WHERE role = 'admin' AND status = 'active'");
        $admins = $db->resultSet();
        
        $subject = 'Email Processing System Alert';
        $message = "The email processing system has encountered an error:\n\n";
        $message .= "Error: " . $errorMessage . "\n";
        $message .= "Time: " . date('Y-m-d H:i:s') . "\n\n";
        $message .= "Please check the email server configuration and logs.";
        
        // Send to each admin
        foreach ($admins as $admin) {
            $messageModel->sendMessage(
                1, // System user
                $admin->id,
                $subject,
                $message,
                null,
                'system'
            );
        }
        
        // Update alert file
        touch($alertFile);
        
    } catch (Exception $e) {
        error_log("process_incoming_emails.php - Failed to send failure alert: " . $e->getMessage());
    }
}

// Check for repeated failures and send alert
$heartbeatFile = APPROOT . '/logs/email_processing_heartbeat.txt';
if (file_exists($heartbeatFile)) {
    $heartbeat = json_decode(file_get_contents($heartbeatFile), true);
    if ($heartbeat && isset($heartbeat['error'])) {
        sendFailureAlert($heartbeat['error']);
    }
}
